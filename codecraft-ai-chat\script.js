// DOM Elements
const searchInput = document.querySelector('.search-box input');
const sendButton = document.querySelector('.send-btn');
const categoryTags = document.querySelectorAll('.category-tag');
const reasoningPill = document.querySelector('.reasoning-pill');
const reasoningDropdown = document.querySelector('.reasoning-dropdown');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Set up event listeners
    setupInputHandling();
    setupButtonInteractions();
    setupCategoryTags();
    setupReasoningControls();
    
    // Add some initial animations
    animateElementsOnLoad();
}

function setupInputHandling() {
    // Handle input focus and blur
    searchInput.addEventListener('focus', function() {
        this.parentElement.classList.add('focused');
    });
    
    searchInput.addEventListener('blur', function() {
        this.parentElement.classList.remove('focused');
    });
    
    // Handle Enter key press
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleSendMessage();
        }
    });
    
    // Auto-resize input (if needed)
    searchInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';
    });
}

function setupButtonInteractions() {
    // Send button click
    sendButton.addEventListener('click', handleSendMessage);
    
    // Control buttons hover effects
    const controlButtons = document.querySelectorAll('.control-btn');
    controlButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.05)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

function setupCategoryTags() {
    categoryTags.forEach(tag => {
        tag.addEventListener('click', function() {
            // Remove active class from all tags
            categoryTags.forEach(t => t.classList.remove('active'));
            
            // Add active class to clicked tag
            this.classList.add('active');
            
            // Add the category to the input
            const categoryText = this.textContent.trim();
            searchInput.value = `Tell me about ${categoryText.toLowerCase()}: `;
            searchInput.focus();
            
            // Animate the tag
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
        
        // Hover effects
        tag.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.02)';
        });
        
        tag.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(0) scale(1)';
            }
        });
    });
}

function setupReasoningControls() {
    // Reasoning pill click
    reasoningPill.addEventListener('click', function() {
        this.classList.toggle('active');
        if (this.classList.contains('active')) {
            this.innerHTML = '<i class="fas fa-brain"></i> Reasoning';
        } else {
            this.innerHTML = '<i class="fas fa-comments"></i> Chat';
        }
    });
    
    // Reasoning dropdown click
    reasoningDropdown.addEventListener('click', function() {
        // Toggle dropdown functionality
        this.classList.toggle('expanded');
        
        // Add dropdown animation
        const chevron = this.querySelector('.fa-chevron-down');
        if (this.classList.contains('expanded')) {
            chevron.style.transform = 'rotate(180deg)';
        } else {
            chevron.style.transform = 'rotate(0deg)';
        }
    });
}

function handleSendMessage() {
    const message = searchInput.value.trim();
    
    if (!message) {
        // Add shake animation to input
        searchInput.parentElement.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            searchInput.parentElement.style.animation = '';
        }, 500);
        return;
    }
    
    // Add loading state to send button
    const originalContent = sendButton.innerHTML;
    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    sendButton.disabled = true;
    
    // Simulate sending message (replace with actual API call)
    setTimeout(() => {
        console.log('Sending message:', message);
        
        // Reset button
        sendButton.innerHTML = originalContent;
        sendButton.disabled = false;
        
        // Clear input
        searchInput.value = '';
        
        // Show success feedback
        showNotification('Message sent successfully!', 'success');
        
    }, 1000);
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#6b8e6b' : '#2d2d2d'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function animateElementsOnLoad() {
    // Animate container
    const container = document.querySelector('.container');
    container.style.opacity = '0';
    container.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        container.style.transition = 'all 0.6s ease';
        container.style.opacity = '1';
        container.style.transform = 'translateY(0)';
    }, 100);
    
    // Animate category tags with stagger
    categoryTags.forEach((tag, index) => {
        tag.style.opacity = '0';
        tag.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            tag.style.transition = 'all 0.4s ease';
            tag.style.opacity = '1';
            tag.style.transform = 'translateY(0)';
        }, 300 + (index * 100));
    });
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
    
    .search-box.focused {
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }
    
    .category-tag.active {
        background: linear-gradient(135deg, #4a6a4a 0%, #3a5a3a 100%);
        transform: translateY(-3px) scale(1.02);
    }
    
    .reasoning-pill.active {
        background: linear-gradient(135deg, #4a6a4a 0%, #3a5a3a 100%);
    }
    
    .reasoning-dropdown.expanded {
        background: #1a1a1a;
    }
    
    .fa-chevron-down {
        transition: transform 0.3s ease;
    }
`;
document.head.appendChild(style); 